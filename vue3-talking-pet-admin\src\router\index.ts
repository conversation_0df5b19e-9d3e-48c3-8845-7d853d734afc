import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

// 扩展路由元信息类型
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    roles?: string[]
    icon?: string
  }
}

// 定义路由配置
const routes: Array<RouteRecordRaw> = [
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },

  // 主页重定向到仪表盘
  {
    path: '/',
    redirect: '/dashboard'
  },

  // 仪表盘
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/DashboardView.vue'),
    meta: {
      title: '仪表盘',
      requiresAuth: true,
      roles: ['SuperAdmin', 'Agent'],
      icon: 'DataBoard'
    }
  },

  // 代理商管理 (仅超级管理员)
  {
    path: '/agents',
    name: 'Agents',
    component: () => import('@/views/agents/AgentListView.vue'),
    meta: {
      title: '代理商管理',
      requiresAuth: true,
      roles: ['SuperAdmin'],
      icon: 'UserFilled'
    }
  },

  // 优惠券管理
  {
    path: '/coupons',
    children: [
      {
        path: 'batches',
        name: 'CouponBatches',
        component: () => import('@/views/coupons/BatchListView.vue'),
        meta: {
          title: '优惠券批次',
          requiresAuth: true,
          roles: ['SuperAdmin'],
          icon: 'Tickets'
        }
      },
      {
        path: 'instances',
        name: 'CouponInstances',
        component: () => import('@/views/coupons/InstanceListView.vue'),
        meta: {
          title: '优惠券实例',
          requiresAuth: true,
          roles: ['SuperAdmin', 'Agent'],
          icon: 'Ticket'
        }
      }
    ]
  },

  // 用户管理
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/users/UserListView.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      roles: ['SuperAdmin', 'Agent'],
      icon: 'User'
    }
  },

  // 系统工具 (仅超级管理员)
  {
    path: '/tools',
    children: [
      {
        path: 'coupon-simulator',
        name: 'CouponSimulator',
        component: () => import('@/views/tools/CouponSimulatorView.vue'),
        meta: {
          title: '优惠券模拟器',
          requiresAuth: true,
          roles: ['SuperAdmin'],
          icon: 'Operation'
        }
      }
    ]
  },

  // 个人中心
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/profile/ProfileView.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      roles: ['SuperAdmin', 'Agent'],
      icon: 'Setting'
    }
  },

  // 错误页面
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403View.vue'),
    meta: {
      title: '无权限访问'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404View.vue'),
    meta: {
      title: '页面不存在'
    }
  },

  // 匹配所有路径，重定向到404
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    // 页面切换时滚动到顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router