<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '../store/user'

const route = useRoute()
const user = useUserStore()

// 移动端菜单状态
const isMobileMenuOpen = ref(false)

// 处理菜单关闭
const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
}

// 监听滚动事件以确保在滚动时关闭移动菜单
onMounted(() => {
    window.addEventListener('scroll', closeMobileMenu)
})

onUnmounted(() => {
    window.removeEventListener('scroll', closeMobileMenu)
})
</script>

<template>
    <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <router-link to="/" class="text-blue-600 font-bold text-xl">
                            Vue3 Demo
                        </router-link>
                    </div>

                    <!-- 桌面端导航链接 -->
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <router-link to="/" class="h-full inline-flex items-center px-1 pt-1 border-b-2" :class="route.path === '/'
                            ? 'border-blue-500 text-gray-900'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                            首页
                        </router-link>

                        <router-link to="/about" class="h-full inline-flex items-center px-1 pt-1 border-b-2" :class="route.path === '/about'
                            ? 'border-blue-500 text-gray-900'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                            关于
                        </router-link>

                        <router-link to="/counter" class="h-full inline-flex items-center px-1 pt-1 border-b-2" :class="route.path === '/counter'
                            ? 'border-blue-500 text-gray-900'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                            计数器
                        </router-link>

                        <router-link to="/user" class="h-full inline-flex items-center px-1 pt-1 border-b-2" :class="route.path === '/user'
                            ? 'border-blue-500 text-gray-900'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                            用户
                        </router-link>
                    </div>
                </div>

                <div class="hidden sm:ml-6 sm:flex sm:items-center">
                    <div class="text-sm px-3 py-1 rounded-full"
                        :class="user.isLoggedIn ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                        {{ user.isLoggedIn ? `${user.name} (已登录)` : '未登录' }}
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="flex items-center sm:hidden">
                    <button @click="isMobileMenuOpen = !isMobileMenuOpen"
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none">
                        <span class="sr-only">打开菜单</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div class="sm:hidden" v-show="isMobileMenuOpen">
            <div class="pt-2 pb-3 space-y-1">
                <router-link to="/" class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium" :class="route.path === '/'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'"
                    @click="closeMobileMenu">
                    首页
                </router-link>

                <router-link to="/about" class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium" :class="route.path === '/about'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'"
                    @click="closeMobileMenu">
                    关于
                </router-link>

                <router-link to="/counter" class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium" :class="route.path === '/counter'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'"
                    @click="closeMobileMenu">
                    计数器
                </router-link>

                <router-link to="/user" class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium" :class="route.path === '/user'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'"
                    @click="closeMobileMenu">
                    用户
                </router-link>
            </div>
        </div>
    </nav>
</template>