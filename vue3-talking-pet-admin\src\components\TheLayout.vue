<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import AdminSidebar from './admin/AdminSidebar.vue'
import AdminHeader from './admin/AdminHeader.vue'

const route = useRoute()
const authStore = useAuthStore()

// 判断是否为登录页面
const isLoginPage = computed(() => route.path === '/login')

// 判断是否为错误页面
const isErrorPage = computed(() => ['/403', '/404'].includes(route.path))

// 是否显示管理后台布局
const showAdminLayout = computed(() =>
  authStore.isLoggedIn && !isLoginPage.value && !isErrorPage.value
)
</script>

<template>
  <!-- 登录页面或错误页面：全屏显示 -->
  <div v-if="!showAdminLayout" class="min-h-screen">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>

  <!-- 管理后台布局 -->
  <div v-else class="min-h-screen bg-gray-50">
    <el-container class="min-h-screen">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="bg-white shadow-sm">
        <AdminSidebar />
      </el-aside>

      <!-- 主内容区域 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header height="60px" class="bg-white shadow-sm px-0">
          <AdminHeader />
        </el-header>

        <!-- 主内容 -->
        <el-main class="p-6">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>