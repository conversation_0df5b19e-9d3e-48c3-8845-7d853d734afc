<template>
  <div class="agent-list">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">代理商管理</h1>
        <p class="text-gray-600 mt-1">管理所有代理商账户</p>
      </div>
      <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
        添加代理商
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索代理商用户名或昵称"
            :prefix-icon="Search"
            clearable
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="flex gap-2">
          <el-select v-model="searchForm.status" placeholder="状态" clearable style="width: 120px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 代理商列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <el-table
        v-loading="loading"
        :data="agentList"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        
        <el-table-column prop="username" label="用户名" min-width="120">
          <template #default="{ row }">
            <div class="font-medium">{{ row.username }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="nickname" label="昵称" min-width="120">
          <template #default="{ row }">
            <div>{{ row.nickname || '-' }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'">
              {{ row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="user_count" label="用户数" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="text-blue-600 font-medium">{{ row.user_count || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_sales" label="总销售额" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="text-green-600 font-medium">
              ¥{{ (row.total_sales || 0).toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              link
              :icon="Edit"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              :type="row.status ? 'warning' : 'success'"
              link
              :icon="row.status ? 'Lock' : 'Unlock'"
              @click="handleToggleStatus(row)"
            >
              {{ row.status ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              link
              :icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑代理商对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingAgent ? '编辑代理商' : '添加代理商'"
      width="500px"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="agentFormRef"
        :model="agentForm"
        :rules="agentRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="agentForm.username"
            placeholder="请输入用户名"
            :disabled="!!editingAgent"
          />
        </el-form-item>
        
        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="agentForm.nickname"
            placeholder="请输入昵称"
          />
        </el-form-item>
        
        <el-form-item v-if="!editingAgent" label="密码" prop="password">
          <el-input
            v-model="agentForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item v-if="editingAgent" label="状态" prop="status">
          <el-switch
            v-model="agentForm.status"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="submitLoading"
            @click="handleSubmit"
          >
            {{ editingAgent ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete,
  Lock,
  Unlock
} from '@element-plus/icons-vue'
import { getAgents, createAgent, updateAgent, deleteAgent } from '@/api/admin'
import type { Agent, CreateAgentRequest, UpdateAgentRequest } from '@/types/admin'
import type { QueryParams } from '@/types/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const showCreateDialog = ref(false)
const editingAgent = ref<Agent | null>(null)
const agentList = ref<Agent[]>([])
const agentFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as boolean | undefined
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 代理商表单
interface AgentForm {
  username: string
  nickname: string
  password: string
  status: boolean
}

const agentForm = reactive<AgentForm>({
  username: '',
  nickname: '',
  password: '',
  status: true
})

// 表单验证规则
const agentRules: FormRules<AgentForm> = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { max: 50, message: '昵称长度不能超过 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 获取代理商列表
async function fetchAgents() {
  try {
    loading.value = true
    const params: QueryParams = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      filters: searchForm.status !== undefined ? { status: searchForm.status } : undefined
    }
    
    const response = await getAgents(params)
    if (response.code === 0) {
      agentList.value = response.data.items
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取代理商列表失败:', error)
    ElMessage.error('获取代理商列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  fetchAgents()
}

// 重置搜索
function handleReset() {
  searchForm.keyword = ''
  searchForm.status = undefined
  pagination.page = 1
  fetchAgents()
}

// 排序变化
function handleSortChange({ prop, order }: any) {
  // 实现排序逻辑
  console.log('Sort change:', prop, order)
  fetchAgents()
}

// 分页大小变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchAgents()
}

// 当前页变化
function handleCurrentChange(page: number) {
  pagination.page = page
  fetchAgents()
}

// 编辑代理商
function handleEdit(agent: Agent) {
  editingAgent.value = agent
  agentForm.username = agent.username
  agentForm.nickname = agent.nickname
  agentForm.password = ''
  agentForm.status = agent.status
  showCreateDialog.value = true
}

// 切换状态
async function handleToggleStatus(agent: Agent) {
  try {
    const action = agent.status ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}代理商 "${agent.nickname || agent.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const updateData: UpdateAgentRequest = {
      status: !agent.status
    }
    
    const response = await updateAgent(agent.id, updateData)
    if (response.code === 0) {
      ElMessage.success(`${action}成功`)
      fetchAgents()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 删除代理商
async function handleDelete(agent: Agent) {
  try {
    await ElMessageBox.confirm(
      `确定要删除代理商 "${agent.nickname || agent.username}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    const response = await deleteAgent(agent.id)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      fetchAgents()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
async function handleSubmit() {
  if (!agentFormRef.value) return
  
  try {
    await agentFormRef.value.validate()
    submitLoading.value = true
    
    if (editingAgent.value) {
      // 更新代理商
      const updateData: UpdateAgentRequest = {
        nickname: agentForm.nickname,
        status: agentForm.status
      }
      
      const response = await updateAgent(editingAgent.value.id, updateData)
      if (response.code === 0) {
        ElMessage.success('更新成功')
        handleCloseDialog()
        fetchAgents()
      }
    } else {
      // 创建代理商
      const createData: CreateAgentRequest = {
        username: agentForm.username,
        nickname: agentForm.nickname,
        password: agentForm.password
      }
      
      const response = await createAgent(createData)
      if (response.code === 0) {
        ElMessage.success('创建成功')
        handleCloseDialog()
        fetchAgents()
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
function handleCloseDialog() {
  showCreateDialog.value = false
  editingAgent.value = null
  agentForm.username = ''
  agentForm.nickname = ''
  agentForm.password = ''
  agentForm.status = true
  agentFormRef.value?.resetFields()
}

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAgents()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
