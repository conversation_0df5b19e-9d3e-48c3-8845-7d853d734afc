<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <!-- 错误图标 -->
      <div class="mx-auto h-24 w-24 bg-yellow-100 rounded-full flex items-center justify-center mb-8">
        <el-icon :size="48" color="#E6A23C">
          <QuestionFilled />
        </el-icon>
      </div>

      <!-- 错误信息 -->
      <div class="mb-8">
        <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 mb-2">页面不存在</h2>
        <p class="text-gray-600">
          抱歉，您访问的页面不存在或已被移除。请检查URL是否正确。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-4">
        <el-button type="primary" size="large" @click="goBack">
          <el-icon class="mr-2"><ArrowLeft /></el-icon>
          返回上一页
        </el-button>
        
        <div>
          <el-button type="default" @click="goHome">
            <el-icon class="mr-2"><HomeFilled /></el-icon>
            返回首页
          </el-button>
        </div>
      </div>

      <!-- 常用链接 -->
      <div class="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 class="text-sm font-medium text-gray-800 mb-3">您可能想要访问：</h3>
        <div class="space-y-2">
          <div>
            <el-button type="primary" link @click="router.push('/dashboard')">
              仪表盘
            </el-button>
          </div>
          <div v-if="authStore.isSuper">
            <el-button type="primary" link @click="router.push('/agents')">
              代理商管理
            </el-button>
          </div>
          <div>
            <el-button type="primary" link @click="router.push('/coupons/instances')">
              优惠券管理
            </el-button>
          </div>
          <div>
            <el-button type="primary" link @click="router.push('/users')">
              用户管理
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { QuestionFilled, ArrowLeft, HomeFilled } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 返回上一页
function goBack() {
  router.go(-1)
}

// 返回首页
function goHome() {
  router.push('/dashboard')
}
</script>
