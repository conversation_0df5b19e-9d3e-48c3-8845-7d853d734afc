{"name": "lingo3d-app", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^4.9.5", "vite": "^3.2.11", "vue-tsc": "^0.40.13"}, "prettier": {"trailingComma": "none", "tabWidth": 2, "semi": false, "singleQuote": false}}