<script setup lang="ts">
import { useUserStore } from '../store/user'
import { ref } from 'vue'

const user = useUserStore()

// 表单状态
const newName = ref('')
const newAge = ref<number | ''>('')

// 方法
const updateUserName = () => {
    if (newName.value.trim()) {
        user.updateName(newName.value)
        newName.value = ''
    }
}

const updateUserAge = () => {
    if (newAge.value !== '') {
        user.updateAge(Number(newAge.value))
        newAge.value = ''
    }
}
</script>

<template>
    <div class="min-h-screen bg-gray-50 py-10">
        <div class="max-w-3xl mx-auto">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6 sm:p-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-6">用户信息</h1>

                    <div class="bg-gray-100 rounded-lg p-6 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h2 class="text-xl font-semibold text-gray-800">{{ user.name }}</h2>
                                <p class="text-gray-600">{{ user.age }}岁</p>
                            </div>

                            <div class="text-center bg-white px-4 py-2 rounded-full shadow-sm">
                                <div class="text-sm text-gray-500 mb-1">账号状态</div>
                                <div :class="[
                                    'font-medium rounded-full px-3 py-1',
                                    user.isLoggedIn
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                ]">
                                    {{ user.isLoggedIn ? '已登录' : '未登录' }}
                                </div>
                            </div>
                        </div>

                        <div class="text-lg text-gray-700 p-3 bg-white rounded border border-gray-200 mb-4">
                            {{ user.nameAndAge }}
                        </div>

                        <div class="flex gap-2">
                            <button @click="user.login()"
                                class="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md font-medium transition-colors"
                                :disabled="user.isLoggedIn"
                                :class="{ 'opacity-50 cursor-not-allowed': user.isLoggedIn }">
                                登录
                            </button>

                            <button @click="user.logout()"
                                class="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md font-medium transition-colors"
                                :disabled="!user.isLoggedIn"
                                :class="{ 'opacity-50 cursor-not-allowed': !user.isLoggedIn }">
                                登出
                            </button>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-800 mb-3">更新用户信息</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-700 font-medium mb-2" for="name">用户名:</label>
                                    <div class="flex">
                                        <input id="name" v-model="newName" type="text" placeholder="输入新用户名"
                                            class="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                        <button @click="updateUserName"
                                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-r-md font-medium transition-colors"
                                            :disabled="!newName.trim()"
                                            :class="{ 'opacity-50 cursor-not-allowed': !newName.trim() }">
                                            更新
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-gray-700 font-medium mb-2" for="age">年龄:</label>
                                    <div class="flex">
                                        <input id="age" v-model="newAge" type="number" placeholder="输入新年龄"
                                            class="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            min="0" />
                                        <button @click="updateUserAge"
                                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-r-md font-medium transition-colors"
                                            :disabled="newAge === ''"
                                            :class="{ 'opacity-50 cursor-not-allowed': newAge === '' }">
                                            更新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-100 p-4 text-center">
                    <router-link to="/" class="text-blue-600 hover:text-blue-800 font-medium">
                        返回首页
                    </router-link>
                </div>
            </div>

            <div class="mt-8 bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">组合式API Store 说明</h2>
                <p class="text-gray-600 mb-4">
                    这个用户信息示例使用 Pinia 的组合式API风格定义 Store。这种方式使用 ref、computed 和函数来定义状态、计算属性和操作，与 Vue 3 的组合式API保持一致。
                </p>
                <p class="text-gray-600">
                    与选项式API不同，组合式API风格的Store更加灵活，提供了更好的类型推断，也更适合与其他组合式函数一起使用。
                </p>
            </div>
        </div>
    </div>
</template>