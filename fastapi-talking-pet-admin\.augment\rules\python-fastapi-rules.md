---
type: "manual"
---

# FastAPI+MySQL 模版项目 - AI 编程规范

## 项目结构

- 遵循现有结构：`app/core/`、`app/api/`、`app/models/`
- 新的 API 端点放在 `app/api/endpoints/` 目录下
- 数据库模型放在 `app/models/` 目录下
- 核心工具放在 `app/core/` 目录下

## 代码风格

- 所有函数使用 Python 类型提示
- 遵循 PEP 8 命名规范
- 为类和函数添加文档字符串
- 数据库操作使用 async/await
- 导入顺序：标准库、第三方库、本地模块

## 数据库规范

- 使用 SQLAlchemy ORM 和声明式基类
- 所有模型继承自 `app.core.database.Base`
- 使用依赖注入：`db: Session = Depends(get_db)`
- 用 try/except 处理数据库错误
- 多表操作使用事务

## API 设计规范

- 使用 FastAPI 路由器模式
- 包含 Pydantic 响应模型
- 添加正确的 HTTP 状态码
- 对通用参数使用依赖注入
- 包含错误处理和验证

## API 端点和路由规则

- 每个功能模块在 `app/api/endpoints/` 下创建单独文件
- 文件命名使用下划线：`user_management.py`、`order_system.py`
- 在 `app/api/routes.py` 中注册所有路由
- 路由前缀格式：`/api/v1/{module}`
- 端点命名使用 RESTful 风格：
  - GET `/api/v1/users` - 获取用户列表
  - GET `/api/v1/users/{id}` - 获取单个用户
  - POST `/api/v1/users` - 创建用户
  - PUT `/api/v1/users/{id}` - 更新用户
  - DELETE `/api/v1/users/{id}` - 删除用户
- 为每个路由组添加 tags 标签用于 API 文档分类

## 配置管理

- 所有设置放在 `app/core/config.py`
- 通过 `.env` 文件使用环境变量
- 代码中不写死任何配置值
- 敏感数据只能通过环境变量设置

## 错误处理

- API 错误使用 FastAPI 的 HTTPException
- 记录错误时使用适当的日志级别
- 返回有意义的错误消息
- 优雅处理数据库连接错误

## 测试规范

- 为新端点编写测试
- 使用 pytest 和 pytest-asyncio
- 在测试中模拟数据库操作
- 测试成功和错误情况

## 依赖管理

- 保持 requirements.txt 更新
- 使用具体版本号
- 只添加必要的依赖
- 在注释中说明新依赖的用途

## 安全规范

- 永远不要提交 `.env` 文件
- 敏感数据使用环境变量
- 验证所有输入数据
- 使用正确的 CORS 设置

## 注释规范

- 解释复杂的业务逻辑
- 为 API 端点添加用途说明
- 为未来改进添加 TODO 注释
- 中文开发者可以使用中文注释
