# 数据库连接配置指南

## 概述

本文档详细说明了如何为"它宇宙后台管理系统"配置和连接MySQL数据库。

## 系统要求

- MySQL 8.0+ (推荐，仅保留此版本)
- Python 3.8+
- Conda环境管理器

## ⚠️ 重要：移除MySQL 5.7版本

为了避免版本冲突，建议移除旧版本的MySQL 5.7服务，只保留MySQL 8.0版本。

### 检查当前MySQL服务

```powershell
# 查看所有MySQL相关服务
Get-Service | Where-Object {$_.Name -like "*mysql*" -or $_.DisplayName -like "*mysql*"}
```

### 移除MySQL 5.7服务

如果发现MySQL57服务，请按以下步骤移除：

```powershell
# 1. 停止MySQL57服务
Stop-Service MySQL57 -Force

# 2. 移除MySQL57服务
sc delete MySQL57

# 3. 验证服务已移除
Get-Service | Where-Object {$_.Name -like "*mysql*"}
```

### 清理MySQL 5.7残留文件

```powershell
# 删除MySQL 5.7安装目录（请根据实际路径调整）
# 注意：执行前请确保已备份重要数据
Remove-Item "C:\Program Files\MySQL\MySQL Server 5.7" -Recurse -Force

# 删除MySQL 5.7数据目录（请根据实际路径调整）
# 注意：这会删除所有5.7版本的数据库数据
Remove-Item "C:\ProgramData\MySQL\MySQL Server 5.7" -Recurse -Force
```

### 配置MySQL 8.0为Windows服务（可选）

如果希望MySQL 8.0作为Windows服务自动启动：

```powershell
# 进入MySQL 8.0安装目录
cd "D:\mysql-8.0.42-winx64\mysql-8.0.42-winx64\bin"

# 安装MySQL 8.0为Windows服务
.\mysqld.exe --install MySQL80

# 启动服务
Start-Service MySQL80
```

## 数据库配置信息

### 默认连接参数

```
数据库类型: MySQL
主机地址: localhost (127.0.0.1)
端口: 3306
数据库名: fastapi_talking_pet
用户名: root
密码: root123
字符集: utf8mb4
排序规则: utf8mb4_unicode_ci
```

### 环境变量配置

项目根目录的 `.env` 文件中包含数据库连接字符串：

```env
# 数据库配置
DATABASE_URL=mysql+pymysql://root:root123@localhost:3306/fastapi_talking_pet
DATABASE_ECHO=false
```

## 数据库设置步骤

### 1. 检查MySQL服务状态

```powershell
# 查看MySQL相关服务
Get-Service | Where-Object {$_.Name -like "*mysql*"}

# 检查MySQL版本
mysql --version
```

### 2. 启动MySQL服务

如果MySQL服务未运行，需要先启动：

```powershell
# 方法1: 启动Windows服务 (如果已安装为服务)
Start-Service MySQL57  # 或其他MySQL服务名

# 方法2: 直接启动MySQL服务器 (推荐)
D:\mysql-8.0.42-winx64\mysql-8.0.42-winx64\bin\mysqld.exe --console
```

### 3. 创建数据库

使用MySQL命令行创建数据库：

```sql
-- 连接到MySQL
mysql -u root -proot123

-- 创建数据库
CREATE DATABASE IF NOT EXISTS fastapi_talking_pet 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE fastapi_talking_pet;

-- 验证数据库创建
SHOW DATABASES;
```

### 4. 测试Python应用连接

激活conda环境并运行连接测试：

```powershell
# 激活conda环境
conda activate fastapi-talking-pet-admin

# 运行数据库连接测试
python test_db_connection.py
```

成功输出示例：
```
正在测试数据库连接...
数据库URL: mysql+pymysql://root:root123@localhost:3306/fastapi_talking_pet
✅ 数据库连接成功!
✅ 当前数据库: fastapi_talking_pet
正在创建数据表...
✅ 数据表创建成功!
✅ 数据库中的表: ['users']
```

## VSCode数据库扩展连接

在VSCode中使用数据库扩展连接时，请使用以下配置：

```
连接类型: MySQL
主机名: 127.0.0.1
端口: 3306
用户名: root
密码: root123
数据库: fastapi_talking_pet
```

## 启动应用

数据库配置完成后，可以启动FastAPI应用：

```powershell
# 方法1: 使用启动脚本
.\start_dev.bat

# 方法2: 直接运行
conda activate fastapi-talking-pet-admin
python main.py
```

应用启动后可访问：
- 主页: http://localhost:51127
- API文档: http://localhost:51127/docs
- 健康检查: http://localhost:51127/health

## 常见问题解决

### 问题1: 连接被拒绝 (ERROR 2003)

**原因**: MySQL服务未启动
**解决**: 启动MySQL服务

```powershell
# 检查服务状态
Get-Service | Where-Object {$_.Name -like "*mysql*"}

# 启动服务
Start-Service MySQL服务名
```

### 问题2: 密码错误 (ERROR 1045)

**原因**: root用户密码不正确
**解决**: 
1. 确认密码是否为 `root123`
2. 如果密码不同，更新 `.env` 文件中的 `DATABASE_URL`

### 问题3: 数据库不存在

**原因**: 数据库 `fastapi_talking_pet` 未创建
**解决**: 手动创建数据库

```sql
CREATE DATABASE IF NOT EXISTS fastapi_talking_pet 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 问题4: Python连接失败

**原因**: 缺少依赖包或环境未激活
**解决**: 
1. 激活conda环境: `conda activate fastapi-talking-pet-admin`
2. 安装依赖: `pip install -r requirements.txt`

## 数据库表结构

当前系统包含以下数据表：

- `users`: 用户信息表

更多表结构信息请参考 `app/models/` 目录下的模型文件。

## 安全注意事项

1. **生产环境**: 请更改默认密码
2. **权限控制**: 为应用创建专用数据库用户
3. **连接加密**: 生产环境建议启用SSL连接
4. **备份策略**: 定期备份数据库

## 维护命令

```sql
-- 查看数据库状态
SHOW STATUS;

-- 查看所有表
SHOW TABLES;

-- 查看表结构
DESCRIBE table_name;

-- 备份数据库
mysqldump -u root -p fastapi_talking_pet > backup.sql

-- 恢复数据库
mysql -u root -p fastapi_talking_pet < backup.sql
```

## 自动化脚本

项目提供了自动化脚本来简化数据库设置过程：

### setup_database.sql
SQL脚本文件，包含数据库创建命令：

```sql
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS fastapi_talking_pet
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE fastapi_talking_pet;

-- 显示当前数据库
SELECT DATABASE() as current_database;
```

### setup_database.bat
Windows批处理脚本，自动执行完整的数据库设置流程：

```batch
# 运行自动化设置
.\setup_database.bat
```

该脚本会：
1. 检查MySQL是否可用
2. 创建数据库
3. 激活conda环境
4. 运行连接测试

## 开发环境快速启动

完整的开发环境启动流程：

```powershell
# 1. 启动MySQL服务 (如果未运行)
D:\mysql-8.0.42-winx64\mysql-8.0.42-winx64\bin\mysqld.exe --console

# 2. 新开终端，激活环境
conda activate fastapi-talking-pet-admin

# 3. 测试数据库连接
python test_db_connection.py

# 4. 启动应用
python main.py
```

## 项目文件结构

```
fastapi-talking-pet-admin/
├── docs/
│   ├── database-setup.md          # 本文档
│   └── prd.md                     # 产品需求文档
├── app/
│   ├── core/
│   │   ├── config.py              # 配置文件
│   │   └── database.py            # 数据库连接
│   └── models/                    # 数据模型
├── .env                           # 环境变量配置
├── main.py                        # 应用入口
├── test_db_connection.py          # 数据库连接测试
├── setup_database.sql             # 数据库创建脚本
├── setup_database.bat             # 自动化设置脚本
└── start_dev.bat                  # 开发服务器启动脚本
```

## 故障排除检查清单

遇到连接问题时，请按以下顺序检查：

- [ ] MySQL服务是否运行
- [ ] 端口3306是否被占用
- [ ] 用户名密码是否正确
- [ ] 数据库是否存在
- [ ] conda环境是否激活
- [ ] Python依赖是否安装完整
- [ ] 防火墙是否阻止连接

## 联系支持

如果遇到无法解决的问题，请提供以下信息：

1. 错误信息的完整输出
2. MySQL版本信息
3. Python环境信息
4. 操作系统版本

---

**最后更新**: 2025-07-18
**版本**: 1.0.0
**作者**: 它宇宙后台管理系统开发团队
