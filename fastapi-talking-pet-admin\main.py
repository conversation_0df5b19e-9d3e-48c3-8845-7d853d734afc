#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
它宇宙后台管理系统
一个基于FastAPI+MySQL的后台管理系统
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 导入应用模块
from app.core.config import settings
from app.core.database import init_database, close_database

# 导入路由模块
from app.api.routes import api_router

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""

    # 创建FastAPI应用
    app = FastAPI(
        title=settings.app_name,
        description="它宇宙后台管理系统 - 基于FastAPI+MySQL的后台管理系统",
        version=settings.app_version,
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=settings.cors_methods,
        allow_headers=settings.cors_headers,
    )

    # 注册路由
    app.include_router(api_router)

    # 注册事件处理器
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        logger.info(f"启动 {settings.app_name} v{settings.app_version}")
        try:
            await init_database()
            logger.info("数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")

    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        logger.info("正在关闭应用...")
        try:
            await close_database()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")

    logger.info(f"FastAPI应用创建成功 - {settings.app_name} v{settings.app_version}")

    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    # 直接运行时的配置
    logger.info(f"启动服务器: {settings.host}:{settings.port}")
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if settings.debug else "warning"
    )
