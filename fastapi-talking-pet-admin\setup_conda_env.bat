@echo off
setlocal enabledelayedexpansion

REM ==========================================
REM Configuration Variables - Modify as needed
REM ==========================================
set ENV_NAME=fastapi-talking-pet-admin
set PYTHON_VERSION=3.10

echo ========================================
echo 它宇宙后台管理系统 Environment Setup
echo ========================================
echo.
echo Environment Name: %ENV_NAME%
echo Python Version: %PYTHON_VERSION%
echo.

REM Check if conda is available
echo [1/4] Checking conda...
where conda >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: conda not found in PATH
    echo Please ensure Anaconda or Miniconda is installed and in PATH
    pause
    exit /b 1
)
echo OK: conda found

REM Check if environment exists
echo.
echo [2/4] Checking environment...
call conda info --envs | findstr "%ENV_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo Environment '%ENV_NAME%' already exists
    set /p choice="Remove and recreate? (y/N): "
    if /i "!choice!"=="y" (
        echo Removing environment...
        call conda env remove -n %ENV_NAME% -y
    ) else (
        echo Cancelled
        pause
        exit /b 0
    )
)

REM Create environment
echo.
echo [3/4] Creating environment...
call conda create -n %ENV_NAME% python=%PYTHON_VERSION% -y
if %errorlevel% neq 0 (
    echo ERROR: Failed to create environment
    pause
    exit /b 1
)
echo OK: Environment created

REM Install dependencies
echo.
echo [4/4] Installing dependencies...
call conda activate %ENV_NAME%
if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found
    pause
    exit /b 1
)

pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo OK: Dependencies installed

REM Create .env file
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo OK: .env file created
    )
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo To start the application:
echo 1. conda activate %ENV_NAME%
echo 2. python main.py
echo.
echo Or run: start_app.bat
echo.

set /p start_choice="Start application now? (y/N): "
if /i "%start_choice%"=="y" (
    echo.
    echo Starting application...
    python main.py
)

pause
