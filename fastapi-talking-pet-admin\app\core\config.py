#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用配置文件
"""

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用设置"""

    # 基础配置
    app_name: str = "它宇宙后台管理系统"
    app_version: str = "1.0.0"
    debug: bool = True

    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000

    # 数据库配置
    database_url: str = "mysql+pymysql://username:password@localhost:3306/database_name"
    database_echo: bool = False  # 是否显示SQL语句

    # CORS配置
    cors_origins: list = ["*"]
    cors_methods: list = ["*"]
    cors_headers: list = ["*"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局设置实例
settings = Settings()
